# 套利交易系统配置文件
# ⚠️ 请妥善保管此文件，不要上传到公共代码仓库

# 交易配置
trading:
  # 交易开关
  enabled: true  # 启用交易功能 (设为false则只监控不交易)

  # 交易对 (必须是两个交易所都支持的交易对)
  symbol: "BTC/USDT"

  # 基础配置
  base_currency: "BTC"
  quote_currency: "USDT"

  # 交易量配置
  max_position_size: 0.1  # 最大持仓量
  min_trade_amount: 0.001  # 最小交易量
  max_trade_amount: 0.01   # 单次最大交易量

  # 价差阈值配置
  ma_period: 20            # 移动平均线周期
  min_profit_threshold: 0.0000001  # 最小盈利阈值 (0.00001%) - 超低阈值用于测试交易逻辑
  max_spread_threshold: 0.02   # 最大价差阈值 (2%)

  # 挂单配置
  order_timeout: 30        # 挂单超时时间(秒)
  max_order_retries: 3     # 最大重试次数
  order_refresh_interval: 5 # 挂单刷新间隔(秒)

# 风险管理
risk_management:
  # 仓位管理
  max_exposure: 2000       # 最大风险敞口 (USDT) - 增加到2000以允许测试交易
  position_limit_pct: 0.8  # 仓位限制百分比

  # 止损配置
  stop_loss_pct: 0.05      # 止损百分比 (5%)
  max_loss_per_day: 100    # 单日最大亏损 (USDT)

  # 异常检测
  price_deviation_threshold: 0.1  # 价格偏差阈值 (10%)
  latency_threshold: 1000    # 延迟阈值 (毫秒)

  # 风险告警
  alert_levels:
    low: 0.3      # 低风险阈值
    medium: 0.6   # 中风险阈值
    high: 0.8     # 高风险阈值
    critical: 0.9 # 严重风险阈值

# 交易所配置引用
exchanges:
  # 从exchanges.yaml文件加载
  config_file: "exchanges.yaml"

  # 或者直接在这里配置 (不推荐，安全性较低)
  binance:
    api_key: "demo_api_key_for_paper_trading"  # 模拟交易使用占位符
    secret: "demo_secret_key_for_paper_trading"  # 模拟交易使用占位符
    sandbox: true
    testnet: true

  lighter:
    api_url: "https://api.lighter.xyz"
    private_key: "demo_private_key_for_paper_trading"  # 模拟交易使用占位符
    account_index: 595
    api_key_index: 1

# 日志配置
logging:
  # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
  level: "INFO"

  # 日志文件配置
  log_file: "logs/arbitrage.log"
  max_file_size: "10MB"
  backup_count: 5

  # 控制台输出
  console_logging: true
  colored_logs: true

  # 交易日志
  trade_log_file: "logs/trades.log"
  trade_log_format: "json"  # json 或 csv

  # 结构化日志
  structured_logging: true
  log_format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

  # 特定模块日志级别
  modules:
    trading: "INFO"
    risk: "WARNING"
    websocket: "ERROR"
    database: "INFO"

# 监控配置
monitoring:
  # Web界面配置
  web_host: "localhost"
  web_port: 8000
  web_debug: false

  # 数据库配置 (可选)
  database:
    enabled: false
    type: "sqlite"  # sqlite, postgresql, mysql
    url: "sqlite:///data/arbitrage.db"

  # 指标收集
  metrics:
    enabled: true
    collection_interval: 5  # 秒
    retention_period: 86400  # 24小时 (秒)

  # 告警配置
  alerts:
    enabled: true
    email_notifications: false
    webhook_url: null
    slack_webhook: null

# 性能配置
performance:
  # 异步配置
  max_concurrent_requests: 10
  request_timeout: 30

  # 数据处理
  data_buffer_size: 1000
  processing_interval: 0.1  # 秒

  # 内存管理
  max_memory_usage: "512MB"
  gc_threshold: 0.8

# 安全配置
security:
  # API密钥加密
  encrypt_api_keys: true
  encryption_key_file: ".encryption_key"

  # 访问控制
  allowed_ips: ["127.0.0.1", "localhost"]
  api_rate_limit: 100  # 每分钟请求数

  # 审计日志
  audit_log: true
  audit_log_file: "logs/audit.log"

# 开发配置
development:
  # 调试模式
  debug: false

  # 模拟交易
  paper_trading: true  # 建议在生产环境前先使用模拟交易

  # 测试配置
  test_mode: false
  mock_exchanges: false

  # 性能分析
  profiling: false
  profile_output: "profiles/"

# 备份和恢复
backup:
  # 自动备份
  enabled: true
  backup_interval: 3600  # 1小时 (秒)
  backup_directory: "backups/"
  max_backups: 24  # 保留24个备份文件

  # 备份内容
  include_logs: true
  include_config: true
  include_data: true

# 通知配置
notifications:
  # 邮件通知
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your_app_password"
    from_address: "<EMAIL>"
    to_addresses: ["<EMAIL>"]

  # Slack通知
  slack:
    enabled: false
    webhook_url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
    channel: "#trading-alerts"

  # 微信通知 (可选)
  wechat:
    enabled: false
    corp_id: "your_corp_id"
    corp_secret: "your_corp_secret"
    agent_id: "your_agent_id"

# 高级配置
advanced:
  # 机器学习 (未来功能)
  ml_enabled: false
  model_path: "models/"

  # 多策略支持 (未来功能)
  multi_strategy: false
  strategy_weights: {}

  # 分布式部署 (未来功能)
  distributed: false
  cluster_nodes: []

# 数据库配置
database:
  path: "data/arbitrage.db"    # SQLite数据库文件路径
  backup_dir: "data/backups"   # 备份目录
  auto_backup: true            # 自动备份
  backup_interval_hours: 24    # 备份间隔 (小时)
  data_retention_days: 90      # 数据保留天数
  cache_size: 1000             # 缓存大小
  auto_commit_interval: 60     # 自动提交间隔 (秒)

# 网络配置
network:
  retry_attempts: 3            # 重试次数
  retry_delay: 1               # 重试延迟 (秒)
  connection_timeout: 10       # 连接超时 (秒)
  read_timeout: 30             # 读取超时 (秒)
  keepalive: true              # 保持连接
  proxy:                       # 代理设置 (可选)
    enabled: false
    http: null
    https: null

# 安全配置
security:
  api_key_encryption: false    # API密钥加密 (TODO: 实现)
  rate_limiting: true          # 启用速率限制
  max_requests_per_minute: 60  # 每分钟最大请求数
  whitelist_ips: []           # IP白名单 (空表示允许所有)
  session_timeout: 3600        # 会话超时 (秒)

# 通知配置
notifications:
  enabled: false               # 启用通知
  methods:                     # 通知方式
    email:
      enabled: false
      smtp_server: ""
      smtp_port: 587
      username: ""
      password: ""
      from_address: ""
      to_addresses: []

    webhook:
      enabled: false
      url: ""
      headers: {}

    telegram:
      enabled: false
      bot_token: ""
      chat_id: ""

# 策略配置
strategy:
  type: "ma_based"             # 策略类型
  parameters:
    ma_fast: 10                # 快速移动平均线
    ma_slow: 20                # 慢速移动平均线
    signal_threshold: 0.001    # 信号阈值
    cooldown_period: 60        # 冷却期 (秒)

  signal_filters:              # 信号过滤器
    min_spread: 0.0005         # 最小价差
    max_spread: 0.05           # 最大价差
    min_volume: 1.0            # 最小交易量
    time_windows:              # 交易时间窗口
      enabled: false
      start_hour: 0
      end_hour: 24

# 交易所特定配置
exchange_settings:
  binance:
    order_type: "limit"        # 默认订单类型
    time_in_force: "GTC"       # 订单有效期
    recv_window: 5000          # 接收窗口

  lighter:
    order_type: "market"       # 默认订单类型 (用于对冲)
    max_slippage: 0.001        # 最大滑点

# 系统维护配置
maintenance:
  auto_restart: false          # 自动重启
  restart_time: "02:00"        # 重启时间
  health_check_interval: 300   # 健康检查间隔 (秒)
  cleanup_interval: 86400      # 清理间隔 (秒)
  backup_on_shutdown: true     # 关闭时备份